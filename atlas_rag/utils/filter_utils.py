import json
import jsonschema
from typing import Dict, List, Any, Union

def flatten_and_filter_triplets(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Flattens nested triplets structure and filters out invalid triplets.
    
    Args:
        data: Dictionary containing nested triplets structure
        
    Returns:
        Dictionary with flattened and filtered triplets
    """
    if not isinstance(data, dict):
        return {"fact": []}
    
    result = {"fact": []}
    
    if "fact" not in data or not isinstance(data["fact"], list):
        return result
    
    def flatten_nested_triplets(items):
        """Recursively flatten nested triplets"""
        flattened = []
        
        def process_item(item):
            if isinstance(item, list):
                if len(item) == 3 and all(isinstance(x, str) for x in item):
                    # Valid triplet
                    flattened.append(item)
                elif len(item) == 1 and isinstance(item[0], list):
                    # Nested structure - process the inner list
                    flattened.extend(process_item(item[0]))
                else:
                    # Process each element in the list
                    for sub_item in item:
                        process_item(sub_item)
            return flattened
        
        for item in items:
            process_item(item)
        return flattened
    
    result["fact"] = flatten_nested_triplets(data["fact"])
    return result

def validate_filter_output(output: str) -> Dict[str, Any]:
    """
    Validates and parses JSON output for filtered triplets.
    
    Args:
        output: JSON string containing filtered triplets
        
    Returns:
        Dictionary with validated triplets structure
    """
    try:
        data = json.loads(output)
    except json.JSONDecodeError:
        return {"fact": []}
    
    if not isinstance(data, dict):
        return {"fact": []}
    
    # Ensure "fact" key exists and is a list
    if "fact" not in data or not isinstance(data["fact"], list):
        return {"fact": []}
    
    # Validate each triplet
    valid_facts = []
    for triplet in data["fact"]:
        if isinstance(triplet, list) and len(triplet) == 3 and all(isinstance(item, str) for item in triplet):
            valid_facts.append(triplet)
    
    return {"fact": valid_facts}

# Messages structure for filtering prompts
messages = [
    {
        "role": "system",
        "content": "You are a helpful question-answering system that filters triplets based on relevance to a given question."
    },
    {
        "role": "user",
        "content": """[[ ## question ## ]]
What is the capital of France?

[[ ## fact_before_filter ## ]]
[["Paris", "is", "capital"], ["London", "is", "capital"], ["Berlin", "is", "capital"]]"""
    },
    {
        "role": "assistant",
        "content": """{"fact": [["Paris", "is", "capital"]]}"""
    }
]
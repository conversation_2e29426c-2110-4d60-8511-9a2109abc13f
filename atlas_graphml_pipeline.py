#!/usr/bin/env python3
"""
ATLAS Knowledge Graph Construction Pipeline for GraphML Output
Complete pipeline from PDF processing to GraphML generation using Gemini 2.5 Flash + Qwen3-Embedding-4B

This script implements Choice 1 from atlas_full_pipeline.ipynb:
- Triple extraction → CSV conversion → Concept generation → GraphML conversion
- Outputs GraphML files suitable for networkx-based RAG operations
- Does NOT implement Neo4j functionality (Choice 2)

Author: AutoSchemaKG Pipeline
Date: 2025-07-28
"""

import os
import sys
import time
import json
import subprocess
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

# Set environment variable to avoid tokenizer warnings
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Add the project root to Python path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import setup modules
from setup_llm_generator import setup_gemini_llm_generator
from setup_embedding_model import setup_qwen_embedding_model
from setup_processing_config import create_processing_config, create_output_directories

# Import ATLAS modules
from atlas_rag.kg_construction.triple_extraction import KnowledgeGraphExtractor
from atlas_rag.kg_construction.triple_config import ProcessingConfig


class ATLASGraphMLPipeline:
    """
    Complete ATLAS Knowledge Graph Construction Pipeline for GraphML output.
    
    This pipeline processes PDF documents through the complete ATLAS workflow:
    1. PDF to JSON conversion (if needed)
    2. Triple extraction using LLM
    3. CSV conversion
    4. Concept generation
    5. GraphML conversion for networkx RAG
    """
    
    def __init__(self, dataset_name: str = "atlas_graphml", filename_pattern: str = ""):
        """
        Initialize the ATLAS GraphML pipeline.
        
        Args:
            dataset_name (str): Name for the output dataset directory
            filename_pattern (str): Pattern to match input files (empty for all files)
        """
        self.dataset_name = dataset_name
        self.filename_pattern = filename_pattern
        self.start_time = time.time()
        
        # Initialize components
        self.llm_generator = None
        self.sentence_encoder = None
        self.processing_config = None
        self.kg_extractor = None
        
        # Paths
        self.project_root = Path(__file__).parent
        self.example_data_dir = self.project_root / "example_data"
        self.pdf_data_dir = self.example_data_dir / "pdf_data"
        self.md_data_dir = self.example_data_dir / "md_data"
        self.output_dir = self.project_root / "import" / self.dataset_name
        
        print(f"🎯 ATLAS GraphML Pipeline Initialized")
        print(f"Dataset: {self.dataset_name}")
        print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Using Gemini 2.5 Flash + Qwen3-Embedding-4B")
        print(f"Target: GraphML for networkx RAG (Choice 1 only)")
        print()

    def validate_prerequisites(self) -> bool:
        """
        Validate that all prerequisites are met before running the pipeline.
        
        Returns:
            bool: True if all prerequisites are met
        """
        print("🔍 Validating Prerequisites...")
        print("-" * 50)
        
        # Check config file
        config_file = self.project_root / 'config.ini'
        if not config_file.exists():
            print("❌ config.ini not found")
            print("💡 Please create config.ini with your API keys")
            return False
        print("✅ config.ini found")
        
        # Check example_data directory
        if not self.example_data_dir.exists():
            print("❌ example_data directory not found")
            print("💡 Create this directory and place your JSON files there")
            return False
        
        # Check for input files (JSON format)
        json_files = list(self.example_data_dir.glob("*.json"))
        if not json_files:
            print("❌ No JSON files found in example_data directory")
            print("💡 Convert your PDFs to JSON format first")
            return False
        
        print(f"✅ Found {len(json_files)} JSON input files:")
        for json_file in json_files:
            print(f"   - {json_file.name}")
        
        # Check import directory
        if not (self.project_root / "import").exists():
            (self.project_root / "import").mkdir(exist_ok=True)
        print("✅ Import directory ready")
        
        print("✅ All prerequisites validated")
        print()
        return True

    def setup_models(self) -> bool:
        """
        Initialize the LLM and embedding models.
        
        Returns:
            bool: True if models were successfully initialized
        """
        print("1️⃣ Initializing Models...")
        print("-" * 50)
        
        try:
            # Initialize Gemini 2.5 Flash LLM
            print("🚀 Setting up Gemini 2.5 Flash LLM...")
            self.llm_generator = setup_gemini_llm_generator()
            print("✅ Gemini 2.5 Flash initialized successfully")
            
            # Initialize Qwen3-Embedding-4B
            print("🚀 Setting up Qwen3-Embedding-4B...")
            self.sentence_encoder = setup_qwen_embedding_model()
            print("✅ Qwen3-Embedding-4B initialized successfully")
            
            print("✅ All models initialized successfully")
            print()
            return True
            
        except Exception as e:
            print(f"❌ Model initialization failed: {str(e)}")
            return False

    def setup_configuration(self) -> bool:
        """
        Setup processing configuration and create output directories.
        
        Returns:
            bool: True if configuration was successful
        """
        print("2️⃣ Setting up Processing Configuration...")
        print("-" * 50)
        
        try:
            # Create output directories
            create_output_directories(self.dataset_name)
            print("✅ Output directories created")
            
            # Create processing configuration
            self.processing_config = create_processing_config(
                dataset_name=self.dataset_name,
                filename_pattern=self.filename_pattern
            )
            print("✅ Processing configuration created")
            
            # Create knowledge graph extractor
            self.kg_extractor = KnowledgeGraphExtractor(
                model=self.llm_generator,
                config=self.processing_config
            )
            print("✅ Knowledge graph extractor initialized")
            
            print("✅ Configuration setup complete")
            print()
            return True
            
        except Exception as e:
            print(f"❌ Configuration setup failed: {str(e)}")
            return False

    def run_triple_extraction(self) -> bool:
        """
        Execute the triple extraction step.

        Returns:
            bool: True if triple extraction was successful
        """
        print("3️⃣ Extracting Triples from Documents...")
        print("-" * 50)
        print("🔍 Extracting entities, relations, and events from your documents")
        print("🎯 Using Gemini 2.5 Flash for high-quality knowledge extraction")
        print("⏱️  This may take several minutes depending on document size")
        print("💡 Some data validation warnings are normal and expected")
        print()

        try:
            extraction_start = time.time()

            # Check if extraction output already exists
            extraction_dir = self.output_dir / "kg_extraction"
            if extraction_dir.exists() and list(extraction_dir.glob("*.json")):
                print("ℹ️  Found existing extraction files - skipping extraction")
                print("💡 Delete the kg_extraction directory to re-run extraction")
            else:
                print("🚀 Starting triple extraction...")
                self.kg_extractor.run_extraction()

            extraction_time = time.time() - extraction_start

            # Verify extraction output
            extraction_files = list(extraction_dir.glob("*.json")) if extraction_dir.exists() else []
            if extraction_files:
                print(f"✅ Found {len(extraction_files)} extraction output files")
                for file in extraction_files[:3]:  # Show first 3 files
                    print(f"   - {file.name}")
                if len(extraction_files) > 3:
                    print(f"   ... and {len(extraction_files) - 3} more files")
            else:
                print("⚠️  No extraction output files found")
                return False

            print(f"✅ Triple extraction completed in {extraction_time/60:.1f} minutes")
            print()
            return True

        except Exception as e:
            print(f"❌ Triple extraction failed: {str(e)}")
            print("💡 This might be due to API rate limits or data format issues")
            print("💡 Try reducing batch_size_triple in the configuration")
            return False

    def convert_to_csv(self) -> bool:
        """
        Convert JSON triples to CSV format.
        
        Returns:
            bool: True if CSV conversion was successful
        """
        print("4️⃣ Converting to CSV Format...")
        print("-" * 50)
        print("📊 Converting extracted triples to CSV format for processing")
        
        try:
            self.kg_extractor.convert_json_to_csv()
            print("✅ CSV conversion completed")
            print()
            return True
            
        except Exception as e:
            print(f"❌ CSV conversion failed: {str(e)}")
            return False

    def generate_concepts(self) -> bool:
        """
        Generate high-level concepts from extracted triples.
        
        Returns:
            bool: True if concept generation was successful
        """
        print("5️⃣ Generating Concepts...")
        print("-" * 50)
        print("🧠 Creating higher-level concepts from extracted triples")
        print("🎯 Using Gemini 2.5 Flash for intelligent concept synthesis")
        
        try:
            concept_start = time.time()
            
            # Generate concept CSV (temporary)
            self.kg_extractor.generate_concept_csv_temp()
            print("✅ Temporary concept CSV generated")
            
            # Create final concept CSV
            self.kg_extractor.create_concept_csv()
            
            concept_time = time.time() - concept_start
            print(f"✅ Concept generation completed in {concept_time/60:.1f} minutes")
            print()
            return True
            
        except Exception as e:
            print(f"❌ Concept generation failed: {str(e)}")
            return False

    def convert_to_graphml(self) -> bool:
        """
        Convert CSV data to GraphML format for networkx RAG.
        This implements Choice 1 from the atlas_full_pipeline.ipynb.

        Returns:
            bool: True if GraphML conversion was successful
        """
        print("6️⃣ Converting to GraphML (Choice 1)...")
        print("-" * 50)
        print("🌐 Creating GraphML files for networkx-based RAG operations")
        print("📈 This enables efficient graph-based retrieval and reasoning")
        print("⚠️  Note: This implements Choice 1 only (GraphML), NOT Choice 2 (Neo4j)")

        try:
            # Convert to GraphML using the Choice 1 approach
            self.kg_extractor.convert_to_graphml()

            # Verify GraphML files were created
            graphml_files = list(self.output_dir.rglob("*.graphml"))
            if graphml_files:
                print(f"✅ Created {len(graphml_files)} GraphML file(s):")
                for graphml_file in graphml_files:
                    rel_path = graphml_file.relative_to(self.project_root)
                    print(f"   - {rel_path}")
            else:
                print("⚠️  No GraphML files found - this may indicate an issue")

            print("✅ GraphML conversion completed")
            print("🎉 GraphML files are ready for networkx RAG operations")
            print()
            return True

        except Exception as e:
            print(f"❌ GraphML conversion failed: {str(e)}")
            print("💡 Check that CSV files were generated correctly in previous steps")
            return False

    def generate_summary(self) -> None:
        """
        Generate a summary of the pipeline execution and output files.
        """
        print("7️⃣ Generating Pipeline Summary...")
        print("-" * 50)

        total_time = time.time() - self.start_time

        # Find output files
        output_files = []
        graphml_files = []
        csv_files = []
        json_files = []

        if self.output_dir.exists():
            # Find different types of output files
            graphml_files = list(self.output_dir.rglob("*.graphml"))
            csv_files = list(self.output_dir.rglob("*.csv"))
            json_files = list(self.output_dir.rglob("*.json"))

            output_files.extend(graphml_files)
            output_files.extend(csv_files)
            output_files.extend(json_files)

        print(f"🎯 ATLAS GraphML Pipeline Summary")
        print(f"=" * 50)
        print(f"Dataset: {self.dataset_name}")
        print(f"Total execution time: {total_time/60:.1f} minutes")
        print(f"Output directory: {self.output_dir}")
        print(f"Generated files: {len(output_files)}")

        # Detailed file breakdown
        if graphml_files:
            print(f"\n🌐 GraphML Files ({len(graphml_files)}):")
            for file_path in sorted(graphml_files):
                rel_path = file_path.relative_to(self.project_root)
                file_size = file_path.stat().st_size / 1024  # KB
                print(f"   - {rel_path} ({file_size:.1f} KB)")

        if csv_files:
            print(f"\n📊 CSV Files ({len(csv_files)}):")
            for file_path in sorted(csv_files):
                rel_path = file_path.relative_to(self.project_root)
                file_size = file_path.stat().st_size / 1024  # KB
                print(f"   - {rel_path} ({file_size:.1f} KB)")

        if json_files:
            print(f"\n📄 JSON Files ({len(json_files)}):")
            for file_path in sorted(json_files):
                rel_path = file_path.relative_to(self.project_root)
                file_size = file_path.stat().st_size / 1024  # KB
                print(f"   - {rel_path} ({file_size:.1f} KB)")

        print(f"\n✅ Pipeline completed successfully!")
        print(f"🌐 GraphML files are ready for networkx-based RAG operations")
        print(f"💡 Next steps:")
        print(f"   1. Load GraphML files with networkx: nx.read_graphml('path/to/file.graphml')")
        print(f"   2. Use the graph for RAG-based question answering")
        print(f"   3. Implement graph traversal algorithms for knowledge retrieval")

        # Save summary to file
        summary_file = self.output_dir / "pipeline_summary.txt"
        with open(summary_file, 'w') as f:
            f.write(f"ATLAS GraphML Pipeline Summary\n")
            f.write(f"=" * 50 + "\n")
            f.write(f"Dataset: {self.dataset_name}\n")
            f.write(f"Execution time: {total_time/60:.1f} minutes\n")
            f.write(f"Generated files: {len(output_files)}\n")
            f.write(f"GraphML files: {len(graphml_files)}\n")
            f.write(f"CSV files: {len(csv_files)}\n")
            f.write(f"JSON files: {len(json_files)}\n")

        print(f"📝 Summary saved to: {summary_file.relative_to(self.project_root)}")

    def process_pdfs_to_json(self) -> bool:
        """
        Convert PDF files to JSON format using the markdown conversion pipeline.
        This is a utility function for processing new PDFs.

        Returns:
            bool: True if PDF processing was successful
        """
        print("📄 PDF to JSON Conversion (Optional)")
        print("-" * 50)

        # Check if PDFs exist
        if not self.pdf_data_dir.exists():
            print("ℹ️  No PDF directory found - skipping PDF processing")
            return True

        pdf_files = list(self.pdf_data_dir.glob("*.pdf"))
        if not pdf_files:
            print("ℹ️  No PDF files found - skipping PDF processing")
            return True

        print(f"📄 Found {len(pdf_files)} PDF files to process")

        # Check if markdown directory exists
        if not self.md_data_dir.exists():
            print("⚠️  Markdown directory not found")
            print("💡 To process PDFs, first convert them to markdown using the pdf_process tool")
            print("💡 See README.md PDF Support section for instructions")
            return False

        # Check for corresponding markdown files
        md_files = list(self.md_data_dir.glob("*.md"))
        print(f"📝 Found {len(md_files)} markdown files")

        if len(md_files) < len(pdf_files):
            print("⚠️  Some PDFs may not have been converted to markdown")
            print("💡 Convert PDFs to markdown first using the pdf_process tool")

        # Convert markdown to JSON
        if md_files:
            print("🔄 Converting markdown files to JSON...")
            try:
                # Run the markdown to JSON conversion
                cmd = [
                    sys.executable, "-m",
                    "atlas_rag.kg_construction.utils.md_processing.markdown_to_json",
                    "--input", str(self.md_data_dir),
                    "--output", str(self.example_data_dir)
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)

                if result.returncode == 0:
                    print("✅ Markdown to JSON conversion completed")
                    print(result.stdout)
                    return True
                else:
                    print(f"❌ Markdown to JSON conversion failed: {result.stderr}")
                    return False

            except Exception as e:
                print(f"❌ Error during markdown to JSON conversion: {str(e)}")
                return False

        return True

    def run_complete_pipeline(self) -> bool:
        """
        Execute the complete ATLAS GraphML pipeline.
        
        Returns:
            bool: True if the entire pipeline was successful
        """
        print("🚀 Starting ATLAS GraphML Pipeline")
        print("=" * 60)
        
        # Validate prerequisites
        if not self.validate_prerequisites():
            print("❌ Prerequisites not met. Please address the issues above.")
            return False
        
        # Execute pipeline steps
        pipeline_steps = [
            ("PDF Processing", self.process_pdfs_to_json),  # Optional step
            ("Setup Models", self.setup_models),
            ("Setup Configuration", self.setup_configuration),
            ("Triple Extraction", self.run_triple_extraction),
            ("CSV Conversion", self.convert_to_csv),
            ("Concept Generation", self.generate_concepts),
            ("GraphML Conversion", self.convert_to_graphml),
        ]
        
        for step_name, step_function in pipeline_steps:
            if not step_function():
                print(f"❌ Pipeline failed at step: {step_name}")
                return False
        
        # Generate summary
        self.generate_summary()
        return True


def main():
    """
    Main entry point for the ATLAS GraphML pipeline.
    """
    import argparse
    
    parser = argparse.ArgumentParser(
        description="ATLAS Knowledge Graph Construction Pipeline for GraphML Output"
    )
    parser.add_argument(
        "--dataset-name", 
        type=str, 
        default="atlas_graphml",
        help="Name for the output dataset directory (default: atlas_graphml)"
    )
    parser.add_argument(
        "--filename-pattern", 
        type=str, 
        default="",
        help="Pattern to match input files (default: process all files)"
    )
    
    args = parser.parse_args()
    
    # Create and run pipeline
    pipeline = ATLASGraphMLPipeline(
        dataset_name=args.dataset_name,
        filename_pattern=args.filename_pattern
    )
    
    try:
        success = pipeline.run_complete_pipeline()
        if success:
            print("\n🎉 ATLAS GraphML Pipeline completed successfully!")
            sys.exit(0)
        else:
            print("\n💥 ATLAS GraphML Pipeline failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️  Pipeline interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
